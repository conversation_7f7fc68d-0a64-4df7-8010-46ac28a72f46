import { NextRequest, NextResponse } from 'next/server'
import { themePresets } from '@/config/theme'

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: {
        presets: themePresets,
        total: themePresets.length,
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch theme presets',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the theme data
    if (!body.name || !body.colors) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid theme data. Name and colors are required.',
        },
        { status: 400 }
      )
    }

    // In a real application, you would save this to a database
    const newTheme = {
      id: `custom-${Date.now()}`,
      name: body.name,
      displayName: body.displayName || body.name,
      description: body.description || 'Custom theme',
      colors: body.colors,
      radius: body.radius || '0.5rem',
      category: 'custom',
      createdAt: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: newTheme,
      message: 'Theme created successfully',
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create theme',
      },
      { status: 500 }
    )
  }
}
