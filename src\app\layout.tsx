import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { cn } from '@/lib/utils'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: {
    default: 'Next.js Theme Boilerplate',
    template: '%s | Next.js Theme Boilerplate',
  },
  description: 'A comprehensive Next.js 15 theme boilerplate with TypeScript, Tailwind CSS, and advanced theming system',
  keywords: ['Next.js', 'React', 'TypeScript', 'Tailwind CSS', 'Theme', 'Boilerplate'],
  authors: [{ name: 'Your Name' }],
  creator: 'Your Name',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://your-domain.com',
    title: 'Next.js Theme Boilerplate',
    description: 'A comprehensive Next.js 15 theme boilerplate with TypeScript, Tailwind CSS, and advanced theming system',
    siteName: 'Next.js Theme Boilerplate',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Next.js Theme Boilerplate',
    description: 'A comprehensive Next.js 15 theme boilerplate with TypeScript, Tailwind CSS, and advanced theming system',
    creator: '@yourusername',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.variable, 'min-h-screen bg-background font-sans antialiased')}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
