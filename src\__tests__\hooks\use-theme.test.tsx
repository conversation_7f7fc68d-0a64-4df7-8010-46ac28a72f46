import { renderHook, act } from '@testing-library/react'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { useTheme } from '@/hooks/use-theme'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock as any

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: query.includes('dark'),
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

describe('useTheme', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
  })

  it('should return theme context', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    expect(result.current).toHaveProperty('theme')
    expect(result.current).toHaveProperty('setTheme')
    expect(result.current).toHaveProperty('resolvedTheme')
    expect(result.current).toHaveProperty('systemTheme')
  })

  it('should set theme correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    act(() => {
      result.current.setTheme('dark')
    })
    
    expect(result.current.theme).toBe('dark')
  })

  it('should resolve system theme correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper })
    
    act(() => {
      result.current.setTheme('system')
    })
    
    expect(result.current.theme).toBe('system')
    expect(result.current.resolvedTheme).toBe('dark') // Based on our mock
  })

  it('should throw error when used outside provider', () => {
    const { result } = renderHook(() => useTheme())
    
    expect(result.error).toEqual(
      Error('useTheme must be used within a ThemeProvider')
    )
  })
})
