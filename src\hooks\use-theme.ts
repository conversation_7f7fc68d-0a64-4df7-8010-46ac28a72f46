'use client'

import { useContext } from 'react'
import { ThemeContext } from '@/components/providers/theme-provider'
import type { ThemeContextType } from '@/types/theme'

/**
 * Hook to access theme context
 * Must be used within a ThemeProvider
 */
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext)
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  
  return context
}
