# Component Documentation

This document provides detailed information about all the UI components available in the theme boilerplate.

## Base Components

### Button

A versatile button component with multiple variants and sizes.

```tsx
import { Button } from '@/components/ui/button'

// Basic usage
<Button>Click me</Button>

// Variants
<Button variant="secondary">Secondary</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="destructive">Destructive</Button>

// Sizes
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
<Button size="icon">🎨</Button>

// As child component
<Button asChild>
  <Link href="/about">About</Link>
</Button>
```

**Props:**
- `variant`: 'default' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'link'
- `size`: 'default' | 'sm' | 'lg' | 'icon'
- `asChild`: boolean - Renders as child component

### Card

A flexible container component for grouping related content.

```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

### Input

Form input component with consistent styling.

```tsx
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

<div className="grid gap-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter your email" />
</div>
```

### Badge

Small status indicators and labels.

```tsx
import { Badge } from '@/components/ui/badge'

<Badge>Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="outline">Outline</Badge>
<Badge variant="destructive">Error</Badge>
```

### Checkbox

Accessible checkbox component.

```tsx
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'

<div className="flex items-center space-x-2">
  <Checkbox id="terms" />
  <Label htmlFor="terms">Accept terms</Label>
</div>
```

### Switch

Toggle switch component.

```tsx
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

<div className="flex items-center space-x-2">
  <Switch id="notifications" />
  <Label htmlFor="notifications">Enable notifications</Label>
</div>
```

## Layout Components

### Header

Main navigation header with theme toggle and search.

```tsx
import { Header } from '@/components/layout/header'

<Header />
```

### Footer

Site footer with links and attribution.

```tsx
import { Footer } from '@/components/layout/footer'

<Footer />
```

## Theme Components

### ThemeToggle

Dropdown menu for switching between light, dark, and system themes.

```tsx
import { ThemeToggle } from '@/components/theme-toggle'

<ThemeToggle />
```

## Dropdown Menu

Accessible dropdown menu component.

```tsx
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="outline">Open Menu</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Item 1</DropdownMenuItem>
    <DropdownMenuItem>Item 2</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

## Customization

All components can be customized using:

1. **CSS Variables** - Modify theme colors and spacing
2. **Tailwind Classes** - Override default styles
3. **Component Props** - Use built-in variants and options
4. **Custom Variants** - Extend using class-variance-authority

### Example: Custom Button Variant

```tsx
import { Button, buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'

// Using className
<Button className="bg-purple-500 hover:bg-purple-600">
  Custom Purple
</Button>

// Extending variants
const customButtonVariants = cva(
  buttonVariants.base,
  {
    variants: {
      ...buttonVariants.variants,
      variant: {
        ...buttonVariants.variants.variant,
        purple: 'bg-purple-500 text-white hover:bg-purple-600',
      },
    },
  }
)
```

## Accessibility

All components follow accessibility best practices:

- Proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance

## Testing

Components include comprehensive tests:

```bash
# Run component tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```
