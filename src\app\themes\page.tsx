import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/components/layout'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ThemeToggle } from '@/components/theme-toggle'

export default function ThemesPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold tracking-tight mb-4">Theme System</h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Advanced theming system with light/dark mode support and customizable color schemes
            </p>
          </div>

          <div className="grid gap-8">
            {/* Theme Toggle Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  Theme Toggle
                  <Badge variant="secondary">Interactive</Badge>
                </CardTitle>
                <CardDescription>
                  Switch between light, dark, and system themes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <ThemeToggle />
                  <p className="text-sm text-muted-foreground">
                    Click the theme toggle to switch between light, dark, and system preference modes.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Color Palette Section */}
            <Card>
              <CardHeader>
                <CardTitle>Color Palette</CardTitle>
                <CardDescription>
                  Semantic color tokens that adapt to the current theme
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-background border"></div>
                      <p className="text-xs font-medium">Background</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-foreground"></div>
                      <p className="text-xs font-medium">Foreground</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-primary"></div>
                      <p className="text-xs font-medium">Primary</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-secondary"></div>
                      <p className="text-xs font-medium">Secondary</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-muted"></div>
                      <p className="text-xs font-medium">Muted</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-accent"></div>
                      <p className="text-xs font-medium">Accent</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-destructive"></div>
                      <p className="text-xs font-medium">Destructive</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 rounded-md bg-card border"></div>
                      <p className="text-xs font-medium">Card</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Theme Features Section */}
            <Card>
              <CardHeader>
                <CardTitle>Theme Features</CardTitle>
                <CardDescription>
                  Built-in features for theme management and customization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="font-medium">🌓 Automatic Detection</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically detects system preference and applies the appropriate theme
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">💾 Persistent Storage</h4>
                    <p className="text-sm text-muted-foreground">
                      Theme preferences are saved to localStorage and persist across sessions
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">🎨 CSS Variables</h4>
                    <p className="text-sm text-muted-foreground">
                      Uses CSS custom properties for dynamic theme switching without page reload
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">⚡ Performance</h4>
                    <p className="text-sm text-muted-foreground">
                      Optimized theme switching with minimal layout shift and smooth transitions
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customization Section */}
            <Card>
              <CardHeader>
                <CardTitle>Customization</CardTitle>
                <CardDescription>
                  How to customize and extend the theme system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Adding Custom Colors</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Extend the color palette by adding new CSS variables and Tailwind classes:
                    </p>
                    <div className="bg-muted p-4 rounded-md">
                      <code className="text-sm">
                        {`--custom-color: 210 40% 98%;`}
                      </code>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Creating Theme Presets</h4>
                    <p className="text-sm text-muted-foreground">
                      Define custom theme presets in the theme configuration file for quick switching between different color schemes.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
