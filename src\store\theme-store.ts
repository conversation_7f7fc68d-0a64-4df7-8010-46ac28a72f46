import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { ThemeState, ThemeActions, ThemeMode, ThemeConfig, CustomTheme, ThemePreset } from '@/types/theme'
import { defaultTheme, themePresets } from '@/config/theme'

interface ThemeStore extends ThemeState, ThemeActions {}

export const useThemeStore = create<ThemeStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentMode: 'system',
      currentTheme: defaultTheme,
      customThemes: [],
      presets: themePresets,
      isLoading: false,
      error: null,

      // Actions
      setMode: (mode: ThemeMode) => {
        set({ currentMode: mode })
      },

      setTheme: (theme: ThemeConfig) => {
        set({ currentTheme: theme })
      },

      addCustomTheme: (theme: CustomTheme) => {
        set((state) => ({
          customThemes: [...state.customThemes, theme],
        }))
      },

      updateCustomTheme: (id: string, updates: Partial<CustomTheme>) => {
        set((state) => ({
          customThemes: state.customThemes.map((theme) =>
            theme.id === id
              ? { ...theme, ...updates, updatedAt: new Date().toISOString() }
              : theme
          ),
        }))
      },

      deleteCustomTheme: (id: string) => {
        set((state) => ({
          customThemes: state.customThemes.filter((theme) => theme.id !== id),
        }))
      },

      loadPresets: async () => {
        set({ isLoading: true, error: null })
        try {
          // In a real app, this would fetch from an API
          const response = await fetch('/api/themes')
          if (!response.ok) throw new Error('Failed to load presets')
          
          const data = await response.json()
          set({ presets: data.data.presets, isLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load presets',
            isLoading: false 
          })
        }
      },

      exportTheme: (themeId: string) => {
        const { customThemes, presets } = get()
        const theme = [...customThemes, ...presets].find((t) => t.id === themeId)
        
        if (!theme) {
          throw new Error('Theme not found')
        }

        const exportData = {
          id: theme.id,
          name: theme.name,
          displayName: theme.displayName,
          colors: theme.colors,
          radius: theme.radius || '0.5rem',
          exportedAt: new Date().toISOString(),
          version: '1.0.0',
        }

        return JSON.stringify(exportData, null, 2)
      },

      importTheme: async (themeData: string) => {
        try {
          const parsed = JSON.parse(themeData)
          
          // Validate required fields
          if (!parsed.name || !parsed.colors) {
            throw new Error('Invalid theme data: missing required fields')
          }

          const newTheme: CustomTheme = {
            id: `imported-${Date.now()}`,
            name: parsed.name,
            displayName: parsed.displayName || parsed.name,
            description: parsed.description || 'Imported theme',
            colors: parsed.colors,
            radius: parsed.radius || '0.5rem',
            isCustom: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          set((state) => ({
            customThemes: [...state.customThemes, newTheme],
          }))

          return newTheme
        } catch (error) {
          throw new Error(
            error instanceof Error ? error.message : 'Failed to import theme'
          )
        }
      },

      resetToDefault: () => {
        set({
          currentMode: 'system',
          currentTheme: defaultTheme,
          customThemes: [],
          presets: themePresets,
          error: null,
        })
      },
    }),
    {
      name: 'theme-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        currentMode: state.currentMode,
        currentTheme: state.currentTheme,
        customThemes: state.customThemes,
      }),
    }
  )
)
