{"semi": false, "trailingComma": "es5", "singleQuote": true, "tabWidth": 2, "useTabs": false, "printWidth": 100, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.js", "tailwindFunctions": ["clsx", "cn", "cva"], "overrides": [{"files": "*.json", "options": {"printWidth": 200}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}]}