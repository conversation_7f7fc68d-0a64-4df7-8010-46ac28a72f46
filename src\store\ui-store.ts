import { create } from 'zustand'

interface UIState {
  // Sidebar state
  sidebarOpen: boolean
  sidebarCollapsed: boolean
  
  // Modal state
  modals: Record<string, boolean>
  
  // Loading states
  loading: Record<string, boolean>
  
  // Toast notifications
  toasts: Array<{
    id: string
    title: string
    description?: string
    type: 'success' | 'error' | 'warning' | 'info'
    duration?: number
  }>
  
  // Search state
  searchOpen: boolean
  searchQuery: string
  
  // Mobile menu state
  mobileMenuOpen: boolean
}

interface UIActions {
  // Sidebar actions
  setSidebarOpen: (open: boolean) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void
  
  // Modal actions
  openModal: (id: string) => void
  closeModal: (id: string) => void
  toggleModal: (id: string) => void
  
  // Loading actions
  setLoading: (id: string, loading: boolean) => void
  
  // Toast actions
  addToast: (toast: Omit<UIState['toasts'][0], 'id'>) => void
  removeToast: (id: string) => void
  clearToasts: () => void
  
  // Search actions
  setSearchOpen: (open: boolean) => void
  setSearchQuery: (query: string) => void
  
  // Mobile menu actions
  setMobileMenuOpen: (open: boolean) => void
  toggleMobileMenu: () => void
}

interface UIStore extends UIState, UIActions {}

export const useUIStore = create<UIStore>((set, get) => ({
  // Initial state
  sidebarOpen: true,
  sidebarCollapsed: false,
  modals: {},
  loading: {},
  toasts: [],
  searchOpen: false,
  searchQuery: '',
  mobileMenuOpen: false,

  // Sidebar actions
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),

  // Modal actions
  openModal: (id) =>
    set((state) => ({
      modals: { ...state.modals, [id]: true },
    })),
  closeModal: (id) =>
    set((state) => ({
      modals: { ...state.modals, [id]: false },
    })),
  toggleModal: (id) =>
    set((state) => ({
      modals: { ...state.modals, [id]: !state.modals[id] },
    })),

  // Loading actions
  setLoading: (id, loading) =>
    set((state) => ({
      loading: { ...state.loading, [id]: loading },
    })),

  // Toast actions
  addToast: (toast) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast = { ...toast, id }
    
    set((state) => ({
      toasts: [...state.toasts, newToast],
    }))

    // Auto-remove toast after duration
    const duration = toast.duration || 5000
    setTimeout(() => {
      get().removeToast(id)
    }, duration)
  },
  removeToast: (id) =>
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== id),
    })),
  clearToasts: () => set({ toasts: [] }),

  // Search actions
  setSearchOpen: (open) => set({ searchOpen: open }),
  setSearchQuery: (query) => set({ searchQuery: query }),

  // Mobile menu actions
  setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),
  toggleMobileMenu: () => set((state) => ({ mobileMenuOpen: !state.mobileMenuOpen })),
}))

// Convenience hooks for specific UI state
export const useSidebar = () => {
  const { sidebarOpen, sidebarCollapsed, setSidebarOpen, setSidebarCollapsed, toggleSidebar } = useUIStore()
  return { sidebarOpen, sidebarCollapsed, setSidebarOpen, setSidebarCollapsed, toggleSidebar }
}

export const useModals = () => {
  const { modals, openModal, closeModal, toggleModal } = useUIStore()
  return { modals, openModal, closeModal, toggleModal }
}

export const useLoading = () => {
  const { loading, setLoading } = useUIStore()
  return { loading, setLoading }
}

export const useToasts = () => {
  const { toasts, addToast, removeToast, clearToasts } = useUIStore()
  return { toasts, addToast, removeToast, clearToasts }
}

export const useSearch = () => {
  const { searchOpen, searchQuery, setSearchOpen, setSearchQuery } = useUIStore()
  return { searchOpen, searchQuery, setSearchOpen, setSearchQuery }
}

export const useMobileMenu = () => {
  const { mobileMenuOpen, setMobileMenuOpen, toggleMobileMenu } = useUIStore()
  return { mobileMenuOpen, setMobileMenuOpen, toggleMobileMenu }
}
