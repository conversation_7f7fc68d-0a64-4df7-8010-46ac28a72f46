import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Get the pathname of the request (e.g. /, /about, /blog/first-post)
  const path = request.nextUrl.pathname

  // Define paths that should be publicly accessible
  const isPublicPath = path === '/' || 
                      path.startsWith('/components') || 
                      path.startsWith('/themes') || 
                      path.startsWith('/examples') || 
                      path.startsWith('/docs') ||
                      path.startsWith('/api/health') ||
                      path.startsWith('/api/themes')

  // Add security headers
  const response = NextResponse.next()

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=()'
  )

  // CSP header for security
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self';"
  )

  // Theme detection from cookies or headers
  const theme = request.cookies.get('theme')?.value || 'system'
  
  // Add theme to response headers for client-side access
  response.headers.set('X-Theme', theme)

  // Rate limiting for API routes (basic implementation)
  if (path.startsWith('/api/')) {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    
    // In a real application, you would implement proper rate limiting
    // using Redis or a similar store
    console.log(`API request from ${ip} to ${path}`)
  }

  return response
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
