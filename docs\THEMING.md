# Theming Guide

This guide covers the advanced theming system built into the Next.js Theme Boilerplate.

## Overview

The theming system provides:

- **Light/Dark Mode** - Automatic system detection and manual toggle
- **Custom Themes** - Create and manage custom color schemes
- **CSS Variables** - Dynamic theme switching without page reload
- **Persistent Storage** - Theme preferences saved across sessions
- **Type Safety** - Full TypeScript support for theme configuration

## Basic Usage

### Theme Provider

Wrap your app with the `ThemeProvider`:

```tsx
import { ThemeProvider } from '@/components/providers/theme-provider'

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
```

### Using the Theme Hook

```tsx
import { useTheme } from '@/hooks/use-theme'

function MyComponent() {
  const { theme, setTheme, resolvedTheme, systemTheme } = useTheme()
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <p>System theme: {systemTheme}</p>
      
      <button onClick={() => setTheme('light')}>Light</button>
      <button onClick={() => setTheme('dark')}>Dark</button>
      <button onClick={() => setTheme('system')}>System</button>
    </div>
  )
}
```

## Color System

### CSS Variables

The theme system uses CSS custom properties for dynamic color switching:

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  /* ... more colors */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  /* ... more colors */
}
```

### Tailwind Integration

Colors are integrated with Tailwind CSS:

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        // ... more colors
      },
    },
  },
}
```

### Using Colors in Components

```tsx
// Using Tailwind classes
<div className="bg-background text-foreground">
  <button className="bg-primary text-primary-foreground">
    Primary Button
  </button>
</div>

// Using CSS variables directly
<div style={{ backgroundColor: 'hsl(var(--background))' }}>
  Content
</div>
```

## Custom Themes

### Creating Custom Themes

```tsx
import type { CustomTheme } from '@/types/theme'

const myCustomTheme: CustomTheme = {
  id: 'ocean-blue',
  name: 'ocean-blue',
  displayName: 'Ocean Blue',
  description: 'A calming blue theme inspired by the ocean',
  colors: {
    light: {
      background: '210 100% 98%',
      foreground: '210 100% 15%',
      primary: '210 100% 50%',
      primaryForeground: '0 0% 100%',
      // ... other colors
    },
    dark: {
      background: '210 100% 8%',
      foreground: '210 100% 90%',
      primary: '210 100% 60%',
      primaryForeground: '210 100% 8%',
      // ... other colors
    },
  },
  radius: '0.75rem',
  isCustom: true,
}
```

### Theme Store

Use Zustand store for theme management:

```tsx
import { useThemeStore } from '@/store/theme-store'

function ThemeManager() {
  const { 
    customThemes, 
    addCustomTheme, 
    deleteCustomTheme,
    exportTheme,
    importTheme 
  } = useThemeStore()

  const handleAddTheme = () => {
    addCustomTheme(myCustomTheme)
  }

  const handleExport = (themeId: string) => {
    const exported = exportTheme(themeId)
    // Save to file or copy to clipboard
  }

  return (
    <div>
      {customThemes.map(theme => (
        <div key={theme.id}>
          <h3>{theme.displayName}</h3>
          <button onClick={() => deleteCustomTheme(theme.id)}>
            Delete
          </button>
        </div>
      ))}
    </div>
  )
}
```

## Advanced Configuration

### Theme Presets

Define theme presets in configuration:

```tsx
// src/config/theme.ts
export const themePresets: ThemePreset[] = [
  {
    id: 'default',
    name: 'default',
    displayName: 'Default',
    description: 'Clean and modern default theme',
    category: 'default',
    colors: {
      // ... color definitions
    },
  },
  {
    id: 'slate',
    name: 'slate',
    displayName: 'Slate',
    description: 'Professional slate gray theme',
    category: 'modern',
    colors: {
      // ... color definitions
    },
  },
]
```

### Server Actions

Use server actions for theme persistence:

```tsx
import { saveCustomTheme } from '@/lib/actions/theme-actions'

async function handleSaveTheme(theme: CustomTheme) {
  const result = await saveCustomTheme(theme)
  
  if (result.success) {
    console.log('Theme saved successfully')
  } else {
    console.error('Failed to save theme:', result.error)
  }
}
```

## Best Practices

### 1. Color Naming

Use semantic color names that describe purpose, not appearance:

```css
/* Good */
--primary: 221.2 83.2% 53.3%;
--destructive: 0 84.2% 60.2%;
--muted: 210 40% 96%;

/* Avoid */
--blue: 221.2 83.2% 53.3%;
--red: 0 84.2% 60.2%;
--gray: 210 40% 96%;
```

### 2. Contrast Ratios

Ensure sufficient contrast for accessibility:

```css
/* Minimum contrast ratios */
/* Normal text: 4.5:1 */
/* Large text: 3:1 */
/* UI components: 3:1 */
```

### 3. Theme Transitions

Disable transitions during theme changes to prevent flashing:

```tsx
<ThemeProvider disableTransitionOnChange>
  {children}
</ThemeProvider>
```

### 4. SSR Considerations

Prevent hydration mismatches:

```tsx
import { useMounted } from '@/hooks/use-mounted'

function ThemeAwareComponent() {
  const mounted = useMounted()
  const { resolvedTheme } = useTheme()
  
  if (!mounted) {
    return <div>Loading...</div>
  }
  
  return (
    <div className={resolvedTheme === 'dark' ? 'dark-styles' : 'light-styles'}>
      Content
    </div>
  )
}
```

## Troubleshooting

### Common Issues

1. **Hydration Mismatch**
   - Use `suppressHydrationWarning` on html element
   - Check for client-only theme logic

2. **Theme Not Persisting**
   - Verify localStorage is available
   - Check storage key configuration

3. **Colors Not Updating**
   - Ensure CSS variables are properly defined
   - Check Tailwind configuration

4. **Performance Issues**
   - Use `disableTransitionOnChange` prop
   - Optimize theme switching logic

### Debug Mode

Enable debug logging:

```tsx
<ThemeProvider
  enableSystem
  storageKey="theme-preference"
  // Add debug logging
  onThemeChange={(theme) => console.log('Theme changed:', theme)}
>
  {children}
</ThemeProvider>
```
