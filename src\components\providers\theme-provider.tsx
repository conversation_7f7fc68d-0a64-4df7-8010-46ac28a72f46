'use client'

import React, { createContext, useEffect, useState, useCallback } from 'react'
import { useLocalStorage, usePrefersDarkMode } from '@/hooks'
import { defaultTheme, themeConfig } from '@/config/theme'
import type { ThemeMode, ThemeContextType, ThemeProviderProps, ThemeConfig } from '@/types/theme'

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({
  children,
  attribute = themeConfig.attribute,
  defaultTheme: defaultThemeProp = themeConfig.defaultTheme,
  enableSystem = themeConfig.enableSystem,
  disableTransitionOnChange = themeConfig.disableTransitionOnChange,
  storageKey = themeConfig.storageKey,
  themes = themeConfig.themes,
  ...props
}: ThemeProviderProps) {
  const [theme, setThemeState] = useLocalStorage<ThemeMode>(storageKey, defaultThemeProp)
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(defaultTheme)
  const [mounted, setMounted] = useState(false)
  const systemTheme = usePrefersDarkMode() ? 'dark' : 'light'

  // Determine the resolved theme
  const resolvedTheme = theme === 'system' ? systemTheme : theme

  // Apply theme to document
  const applyTheme = useCallback(
    (newTheme: 'light' | 'dark') => {
      if (typeof window === 'undefined') return

      const root = window.document.documentElement
      const isDark = newTheme === 'dark'

      // Remove existing theme classes
      root.classList.remove('light', 'dark')

      if (attribute === 'class') {
        root.classList.add(newTheme)
      } else {
        root.setAttribute(attribute, newTheme)
      }

      // Apply CSS custom properties for the current theme
      const colors = currentTheme.colors[newTheme]
      Object.entries(colors).forEach(([key, value]) => {
        const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
        root.style.setProperty(cssVar, value)
      })

      // Apply other theme properties
      root.style.setProperty('--radius', currentTheme.radius)

      // Handle transitions
      if (disableTransitionOnChange) {
        const css = document.createElement('style')
        css.appendChild(
          document.createTextNode(
            `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`
          )
        )
        document.head.appendChild(css)

        return () => {
          // Force repaint
          ;(() => window.getComputedStyle(document.body))()

          // Wait for next tick before removing
          setTimeout(() => {
            document.head.removeChild(css)
          }, 1)
        }
      }
    },
    [attribute, currentTheme, disableTransitionOnChange]
  )

  // Set theme function
  const setTheme = useCallback(
    (newTheme: ThemeMode) => {
      setThemeState(newTheme)
    },
    [setThemeState]
  )

  // Apply theme when resolved theme changes
  useEffect(() => {
    if (!mounted) return
    applyTheme(resolvedTheme)
  }, [resolvedTheme, applyTheme, mounted])

  // Apply theme when current theme config changes
  useEffect(() => {
    if (!mounted) return
    applyTheme(resolvedTheme)
  }, [currentTheme, resolvedTheme, applyTheme, mounted])

  // Set mounted state
  useEffect(() => {
    setMounted(true)
  }, [])

  // Prevent hydration mismatch
  if (!mounted) {
    return null
  }

  const value: ThemeContextType = {
    theme,
    setTheme,
    resolvedTheme,
    systemTheme,
    themes,
    currentTheme,
    setCurrentTheme,
  }

  return (
    <ThemeContext.Provider value={value} {...props}>
      {children}
    </ThemeContext.Provider>
  )
}
