{"name": "nextjs-theme-boilerplate", "version": "1.0.0", "private": true, "description": "A comprehensive Next.js 15 theme boilerplate with TypeScript, Tailwind CSS, and advanced theming system", "keywords": ["nextjs", "react", "typescript", "tailwind", "theme", "boilerplate"], "author": "Your Name", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "husky install"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "next": "15.3.5", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "storybook": "^7.6.6", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}