import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/components/layout'

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold tracking-tight mb-4">Next.js Theme Boilerplate</h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              A comprehensive starter template with TypeScript, Tailwind CSS, and advanced theming system
            </p>
          </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🎨 Theme System
              <Badge variant="secondary">New</Badge>
            </CardTitle>
            <CardDescription>
              Advanced theming with light/dark mode support and custom theme configurations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Built-in theme provider with system preference detection and persistent storage.
            </p>
            <Button variant="outline" size="sm">
              Explore Themes
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🧩 UI Components
              <Badge variant="secondary">Ready</Badge>
            </CardTitle>
            <CardDescription>
              Comprehensive component library built with Radix UI and styled with Tailwind CSS
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Pre-built components following atomic design principles with full TypeScript support.
            </p>
            <Button variant="outline" size="sm">
              View Components
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ⚡ Next.js 15
              <Badge variant="secondary">Latest</Badge>
            </CardTitle>
            <CardDescription>
              Built with the latest Next.js features including App Router and Server Components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Optimized for performance with Turbopack, streaming, and enhanced middleware support.
            </p>
            <Button variant="outline" size="sm">
              Learn More
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔧 Developer Tools
              <Badge variant="secondary">Configured</Badge>
            </CardTitle>
            <CardDescription>
              Complete development setup with TypeScript, ESLint, Prettier, and testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Pre-configured development environment with Git hooks and automated code quality checks.
            </p>
            <Button variant="outline" size="sm">
              Setup Guide
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📱 Responsive Design
              <Badge variant="secondary">Mobile-First</Badge>
            </CardTitle>
            <CardDescription>
              Mobile-first responsive design with flexible grid system and breakpoints
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Optimized for all screen sizes with touch-friendly interactions and accessibility.
            </p>
            <Button variant="outline" size="sm">
              View Examples
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🚀 Production Ready
              <Badge variant="secondary">Optimized</Badge>
            </CardTitle>
            <CardDescription>
              Performance optimized with code splitting, image optimization, and caching
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Built for production with SEO optimization, analytics integration, and deployment configs.
            </p>
            <Button variant="outline" size="sm">
              Deploy Now
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold mb-4">Get Started</h2>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          Clone this repository and start building your next project with a solid foundation.
          All components are customizable and the theme system is fully configurable.
        </p>
        <div className="flex gap-4 justify-center">
          <Button size="lg">
            Get Started
          </Button>
          <Button variant="outline" size="lg">
            View Documentation
          </Button>
          </div>
        </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
