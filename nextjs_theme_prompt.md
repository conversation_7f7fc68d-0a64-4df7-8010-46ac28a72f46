## Architecture Principles

### Scalability Features
- **Modular Architecture**: Each feature is self-contained with its own components, hooks, and utilities
- **Barrel Exports**: Consistent index.ts files for clean imports
- **Route Groups**: Organized app router with logical grouping using parentheses
- **Feature-Based Organization**: Components organized by business logic, not just UI patterns
- **Micro-Frontend Ready**: Structure supports splitting into micro-frontends if needed

### Development Standards
- **Component Co-location**: Stories, tests, and styles alongside components
- **Atomic Design**: UI components follow atomic design principles (atoms, molecules, organisms)
- **Domain-Driven Design**: Features organized by business domains
- **API-First Approach**: API routes and utilities structured for external consumption
- **Configuration-Driven**: Centralized configuration for easy customization

### Next.js 15 Specific Features
- **Route Groups**: `(auth)`, `(dashboard)`, `(marketing)` for logical organization
- **Parallel Routes**: Support for complex layouts with multiple simultaneous routes
- **Intercepting Routes**: Modal and overlay route interception
- **Server Actions**: Integrated server actions for form handling and data mutations
- **Streaming**: Built-in loading.tsx and error.tsx for better UX
- **Metadata API**: Dynamic metadata generation for SEO
- **Static Export**: Configuration for static site generation when needed# Next.js Customizable Theme Boilerplate - Development Prompt

## Project Overview
Create a comprehensive Next.js boilerplate repository that serves as a starting point for frontend projects. This should be a complete, production-ready setup that can be cloned and immediately used for new projects with minimal configuration.

## Core Requirements

### 1. Next.js 15 Setup
- Latest Next.js 15 with enhanced App Router features
- React 18+ with concurrent features
- TypeScript 5.0+ with strict configuration
- Turbopack for faster development builds
- Enhanced middleware support for routing and authentication
- Server Actions and Server Components optimization
- Optimized build configuration with partial pre-rendering
- Environment variable setup (.env.local, .env.example)
- Next.js 15 configuration for images, fonts, and performance optimization

### 2. Theme System Architecture
- **Theme Provider**: React Context-based theme management
- **Theme Configuration**: Centralized theme config file with:
  - Color palettes (primary, secondary, accent, neutral, semantic colors)
  - Typography scales (headings, body text, captions)
  - Spacing system (margins, padding, gaps)
  - Border radius values
  - Shadow definitions
  - Animation/transition presets
- **Theme Switching**: Light/dark mode toggle with system preference detection
- **Theme Persistence**: Local storage integration for theme preferences

### 3. Styling Framework
- **Tailwind CSS**: Pre-configured with custom theme variables
- **CSS Custom Properties**: CSS variables for dynamic theme switching
- **Component Variants**: Styled component variants for different themes
- **Responsive Design**: Mobile-first approach with breakpoint system

### 4. UI Component Library
Create a comprehensive set of reusable components:
- **Layout Components**: Header, Footer, Sidebar, Container, Grid
- **Navigation**: Navbar, Breadcrumbs, Pagination, Tabs
- **Form Elements**: Input, Select, Checkbox, Radio, Button, Form validation
- **Data Display**: Cards, Tables, Lists, Badges, Avatars
- **Feedback**: Alerts, Toasts, Loading states, Progress bars
- **Modals & Overlays**: Modal, Dropdown, Tooltip, Popover
- **Interactive**: Accordion, Carousel, Tabs, Toggle switches

### 5. Development Tools & Setup
- **TypeScript**: Strict configuration with proper type definitions
- **ESLint**: Comprehensive linting rules for code quality
- **Prettier**: Code formatting with consistent style
- **Husky**: Git hooks for pre-commit checks
- **Commitlint**: Conventional commit message formatting
- **Package Manager**: pnpm/npm/yarn with lock file

### 6. State Management
- **React Context**: For theme and global state
- **React Query/TanStack Query**: For server state management (optional setup)
- **Zustand**: For client-side state management (lightweight option)

### 7. Utility Functions
- **Theme Utilities**: Functions for theme manipulation
- **Common Helpers**: Date formatting, string manipulation, validation
- **Responsive Utilities**: Breakpoint helpers, device detection
- **API Utilities**: HTTP client setup, error handling

### 8. Performance Optimization
- **Code Splitting**: Dynamic imports for components
- **Image Optimization**: Next.js Image component setup
- **Font Loading**: Optimized font loading strategy
- **Bundle Analysis**: Webpack bundle analyzer integration
- **Caching Strategy**: Static generation and caching setup

### 9. Testing Setup
- **Jest**: Unit testing configuration
- **React Testing Library**: Component testing utilities
- **Cypress**: E2E testing setup (optional)
- **Testing Utilities**: Custom render functions, mocks

### 10. Documentation
- **README.md**: Comprehensive setup and usage instructions
- **Component Documentation**: Storybook integration or custom docs
- **Theme Guide**: How to customize and extend themes
- **Deployment Guide**: Vercel, Netlify, and other platform instructions

## File Structure (Next.js 15 + App Router)
```
├── src/
│   ├── app/                          # Next.js 15 App Router
│   │   ├── (auth)/                   # Route groups for auth pages
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   └── register/
│   │   │       └── page.tsx
│   │   ├── (dashboard)/              # Route groups for dashboard
│   │   │   ├── analytics/
│   │   │   ├── settings/
│   │   │   └── layout.tsx
│   │   ├── (marketing)/              # Route groups for marketing pages
│   │   │   ├── about/
│   │   │   ├── pricing/
│   │   │   └── layout.tsx
│   │   ├── api/                      # API routes
│   │   │   ├── auth/
│   │   │   │   └── route.ts
│   │   │   ├── themes/
│   │   │   │   └── route.ts
│   │   │   └── health/
│   │   │       └── route.ts
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   ├── loading.tsx               # Global loading UI
│   │   ├── error.tsx                 # Global error UI
│   │   ├── not-found.tsx             # 404 page
│   │   └── page.tsx                  # Home page
│   ├── components/                   # Component library
│   │   ├── ui/                       # Base UI components (shadcn/ui style)
│   │   │   ├── button/
│   │   │   │   ├── button.tsx
│   │   │   │   ├── button.stories.tsx
│   │   │   │   ├── button.test.tsx
│   │   │   │   └── index.ts
│   │   │   ├── input/
│   │   │   ├── modal/
│   │   │   ├── dropdown/
│   │   │   └── index.ts              # Barrel exports
│   │   ├── layout/                   # Layout components
│   │   │   ├── header/
│   │   │   ├── footer/
│   │   │   ├── sidebar/
│   │   │   ├── navigation/
│   │   │   └── index.ts
│   │   ├── forms/                    # Form-specific components
│   │   │   ├── auth-form/
│   │   │   ├── contact-form/
│   │   │   ├── settings-form/
│   │   │   └── index.ts
│   │   ├── features/                 # Feature-specific components
│   │   │   ├── auth/
│   │   │   ├── dashboard/
│   │   │   ├── theme-editor/
│   │   │   ├── user-profile/
│   │   │   └── index.ts
│   │   ├── providers/                # Provider components
│   │   │   ├── theme-provider.tsx
│   │   │   ├── auth-provider.tsx
│   │   │   ├── query-provider.tsx
│   │   │   └── index.ts
│   │   └── index.ts                  # Main barrel export
│   ├── hooks/                        # Custom hooks
│   │   ├── use-theme.ts
│   │   ├── use-auth.ts
│   │   ├── use-local-storage.ts
│   │   ├── use-media-query.ts
│   │   ├── use-debounce.ts
│   │   └── index.ts
│   ├── lib/                          # Utility libraries
│   │   ├── auth/                     # Authentication utilities
│   │   │   ├── config.ts
│   │   │   ├── providers.ts
│   │   │   └── index.ts
│   │   ├── db/                       # Database utilities
│   │   │   ├── connection.ts
│   │   │   ├── queries.ts
│   │   │   └── index.ts
│   │   ├── api/                      # API utilities
│   │   │   ├── client.ts
│   │   │   ├── endpoints.ts
│   │   │   ├── error-handler.ts
│   │   │   └── index.ts
│   │   ├── theme/                    # Theme utilities
│   │   │   ├── config.ts
│   │   │   ├── generator.ts
│   │   │   ├── presets.ts
│   │   │   └── index.ts
│   │   ├── utils/                    # General utilities
│   │   │   ├── cn.ts                 # Class name utility
│   │   │   ├── format.ts
│   │   │   ├── validation.ts
│   │   │   ├── constants.ts
│   │   │   └── index.ts
│   │   ├── validations/              # Schema validations
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   ├── theme.ts
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── styles/                       # Styling system
│   │   ├── themes/                   # Theme configurations
│   │   │   ├── light.ts
│   │   │   ├── dark.ts
│   │   │   ├── custom.ts
│   │   │   └── index.ts
│   │   ├── components/               # Component-specific styles
│   │   │   ├── ui.css
│   │   │   ├── layout.css
│   │   │   └── forms.css
│   │   ├── globals.css               # Global styles
│   │   ├── variables.css             # CSS custom properties
│   │   └── tailwind.css              # Tailwind imports
│   ├── types/                        # TypeScript definitions
│   │   ├── auth.ts
│   │   ├── theme.ts
│   │   ├── api.ts
│   │   ├── database.ts
│   │   ├── components.ts
│   │   ├── globals.ts
│   │   └── index.ts
│   ├── store/                        # State management
│   │   ├── auth/
│   │   │   ├── auth-store.ts
│   │   │   ├── auth-actions.ts
│   │   │   └── index.ts
│   │   ├── theme/
│   │   │   ├── theme-store.ts
│   │   │   ├── theme-actions.ts
│   │   │   └── index.ts
│   │   ├── ui/
│   │   │   ├── ui-store.ts
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── config/                       # Configuration files
│   │   ├── auth.ts
│   │   ├── database.ts
│   │   ├── theme.ts
│   │   ├── api.ts
│   │   ├── constants.ts
│   │   └── index.ts
│   ├── middleware/                   # Custom middleware
│   │   ├── auth-middleware.ts
│   │   ├── theme-middleware.ts
│   │   ├── api-middleware.ts
│   │   └── index.ts
│   └── assets/                       # Static assets in src
│       ├── icons/
│       ├── images/
│       └── fonts/
├── public/                           # Public static files
│   ├── images/
│   ├── icons/
│   ├── favicon.ico
│   ├── robots.txt
│   ├── sitemap.xml
│   └── manifest.json
├── docs/                             # Documentation
│   ├── components/
│   ├── themes/
│   ├── deployment/
│   ├── api/
│   └── README.md
├── tests/                            # Test files
│   ├── __mocks__/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   ├── e2e/
│   └── setup.ts
├── .storybook/                       # Storybook configuration
├── scripts/                          # Build and utility scripts
│   ├── build.js
│   ├── generate-component.js
│   ├── theme-generator.js
│   └── deploy.js
├── .env.example                      # Environment variables template
├── .env.local                        # Local environment variables
├── .gitignore
├── .eslintrc.js
├── .prettierrc
├── next.config.js                    # Next.js 15 configuration
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── pnpm-lock.yaml
├── middleware.ts                     # Next.js middleware
├── jest.config.js
├── playwright.config.ts
└── README.md
```

## Key Features to Implement

### Advanced Next.js 15 Features
- **Server Components**: Optimized server-side rendering for better performance
- **Client Components**: Strategic use of 'use client' directive for interactive components
- **Streaming**: Progressive loading with Suspense boundaries
- **Server Actions**: Form handling and data mutations without API routes
- **Route Handlers**: Modern API route handling with Web API standards
- **Middleware**: Advanced routing, authentication, and theme detection
- **Edge Runtime**: Optimized edge functions for global performance

### Theme Customization Interface
- Visual theme editor page with real-time preview
- Theme inheritance and cascading system
- Component-level theme overrides
- CSS-in-JS integration with theme tokens
- Export/import theme configurations (JSON, CSS, JS)
- Theme presets (Material, Tailwind, Custom, Brand-specific)
- Theme validation and conflict resolution

### Component Showcase
- Interactive component gallery with live editing
- Component playground with prop manipulation
- Auto-generated documentation from TypeScript types
- Theme variant demonstrations across all components
- Accessibility testing interface
- Performance metrics for each component

### Advanced Developer Experience
- Hot reload for theme changes with instant preview
- TypeScript intellisense for theme tokens and component props
- VS Code extensions and snippets for rapid development
- CLI commands for generating components, pages, and features
- Auto-generated API documentation
- Component dependency graphs and usage analytics

## Technical Specifications

### Performance Targets
- First Contentful Paint < 1.5s
- Largest Contentful Paint < 2.5s
- Cumulative Layout Shift < 0.1
- First Input Delay < 100ms

### Browser Support
- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+)
- Progressive enhancement approach
- Responsive design for all device sizes

### Accessibility
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast validation

## Deployment & Distribution

### Repository Structure
- Main branch: Production-ready code
- Development branch: Latest features
- Feature branches: Individual feature development
- Comprehensive branching strategy

### CI/CD Pipeline
- Automated testing on pull requests
- Build verification
- Deployment to staging/production
- Automated dependency updates

### Distribution
- GitHub template repository
- NPM package for components (optional)
- Docker container setup
- Documentation hosting

## Success Criteria
1. **Quick Setup**: Clone and run with `npm install && npm run dev`
2. **Easy Customization**: Change theme with minimal code changes
3. **Production Ready**: Optimized build with proper SEO and performance
4. **Developer Friendly**: Excellent TypeScript support and documentation
5. **Extensible**: Easy to add new components and features
6. **Maintainable**: Clean code structure with proper testing

## Advanced Configuration

### Multi-Environment Support
```typescript
// config/environments.ts
export const environments = {
  development: {
    api: 'http://localhost:3000/api',
    features: ['debugMode', 'devTools'],
    analytics: false
  },
  staging: {
    api: 'https://staging-api.example.com',
    features: ['betaFeatures'],
    analytics: true
  },
  production: {
    api: 'https://api.example.com',
    features: ['stableFeatures'],
    analytics: true
  }
}
```

### Feature Flag System
```typescript
// lib/feature-flags.ts
export const featureFlags = {
  newThemeEditor: true,
  advancedAnalytics: false,
  betaComponents: process.env.NODE_ENV === 'development'
}
```

### Database Integration Options
- **Prisma**: Full ORM with type safety
- **Drizzle**: Lightweight ORM alternative
- **PlanetScale**: Serverless MySQL
- **Supabase**: PostgreSQL with real-time features
- **MongoDB**: NoSQL document database
- **Redis**: Caching and session storage

### Authentication Providers
- **NextAuth.js**: Multiple provider support
- **Clerk**: Complete authentication solution
- **Auth0**: Enterprise authentication
- **Firebase Auth**: Google's authentication service
- **Supabase Auth**: Open-source alternative

### Deployment Configurations
- **Vercel**: Optimized Next.js hosting
- **Netlify**: JAMstack deployment
- **Docker**: Containerized deployment
- **AWS**: EC2, ECS, Lambda deployment
- **Railway**: Simplified cloud deployment