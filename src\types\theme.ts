export type ThemeMode = 'light' | 'dark' | 'system'

export interface ThemeColors {
  background: string
  foreground: string
  card: string
  cardForeground: string
  popover: string
  popoverForeground: string
  primary: string
  primaryForeground: string
  secondary: string
  secondaryForeground: string
  muted: string
  mutedForeground: string
  accent: string
  accentForeground: string
  destructive: string
  destructiveForeground: string
  border: string
  input: string
  ring: string
}

export interface ThemeConfig {
  name: string
  displayName: string
  colors: {
    light: ThemeColors
    dark: ThemeColors
  }
  radius: string
  fontFamily: {
    sans: string[]
    mono: string[]
  }
}

export interface ThemeContextType {
  theme: ThemeMode
  setTheme: (theme: ThemeMode) => void
  resolvedTheme: 'light' | 'dark'
  systemTheme: 'light' | 'dark'
  themes: string[]
  currentTheme: ThemeConfig
  setCurrentTheme: (theme: ThemeConfig) => void
}

export interface ThemeProviderProps {
  children: React.ReactNode
  attribute?: string
  defaultTheme?: ThemeMode
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
  storageKey?: string
  themes?: string[]
  forcedTheme?: string
  value?: ThemeContextType
}

export interface CustomTheme {
  id: string
  name: string
  displayName: string
  description?: string
  author?: string
  version?: string
  colors: {
    light: Partial<ThemeColors>
    dark: Partial<ThemeColors>
  }
  radius?: string
  fontFamily?: {
    sans?: string[]
    mono?: string[]
  }
  createdAt?: string
  updatedAt?: string
  isCustom: boolean
}

export interface ThemePreset {
  id: string
  name: string
  displayName: string
  description: string
  preview: string
  colors: {
    light: ThemeColors
    dark: ThemeColors
  }
  radius: string
  category: 'default' | 'modern' | 'classic' | 'vibrant' | 'minimal'
}

export interface ThemeState {
  currentMode: ThemeMode
  currentTheme: ThemeConfig
  customThemes: CustomTheme[]
  presets: ThemePreset[]
  isLoading: boolean
  error: string | null
}

export interface ThemeActions {
  setMode: (mode: ThemeMode) => void
  setTheme: (theme: ThemeConfig) => void
  addCustomTheme: (theme: CustomTheme) => void
  updateCustomTheme: (id: string, theme: Partial<CustomTheme>) => void
  deleteCustomTheme: (id: string) => void
  loadPresets: () => Promise<void>
  exportTheme: (themeId: string) => string
  importTheme: (themeData: string) => Promise<CustomTheme>
  resetToDefault: () => void
}
